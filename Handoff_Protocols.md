# 🤝 Dynamic Handoff Protocols

## 🎯 Principios de Traspaso Efectivo

### **1. Contexto Completo**
- Transferir no solo el "qué" sino el "por qué" y el "cómo"
- Incluir decisiones tomadas y alternativas descartadas
- Proporcionar contexto histórico y restricciones

### **2. Continuidad Cognitiva**
- Mantener el hilo de pensamiento entre roles
- Preservar la intención original del Estratega
- Asegurar coherencia en el estilo y enfoque

### **3. Validación Bidireccional**
- Confirmación de recepción y comprensión
- Clarificación de dudas antes del traspaso
- Feedback loop para ajustes inmediatos

### **4. Trazabilidad Completa**
- Log de todas las decisiones y cambios
- Historial de traspasos y modificaciones
- Audit trail para accountability

## 🔄 Templates de Traspaso por Rol

### **📋 ESTRATEGA → DISEÑADOR**

#### **Template Estándar:**
```
🎯 CONTEXTO ESTRATÉGICO:
- Visión del proyecto: [descripción clara de la visión]
- Objetivos principales: [lista priorizada de objetivos]
- Stakeholders clave: [quién está involucrado y sus expectativas]
- Restricciones conocidas: [limitaciones de tiempo, presupuesto, tecnología]

🎨 BRIEF DE DISEÑO:
- Problema a resolver: [definición específica del problema]
- Audiencia objetivo: [perfil detallado del usuario final]
- Criterios de éxito: [métricas y KPIs específicos]
- Estilo y tono deseado: [guidelines de marca y comunicación]

🚫 LIMITACIONES Y NO-HACER:
- Restricciones técnicas: [limitaciones conocidas]
- Presupuesto máximo: [límites financieros]
- Deadline crítico: [fechas no negociables]
- Elementos a evitar: [qué NO incluir]

📊 MÉTRICAS DE VALIDACIÓN:
- Cómo medir el éxito: [KPIs específicos]
- Criterios de aceptación: [qué constituye "terminado"]
- Proceso de aprobación: [quién y cómo aprueba]

🔄 PRÓXIMOS PASOS:
Actúa como Diseñador. Tu tarea es crear un plan detallado que materialice esta visión estratégica. Enfócate en [área específica de énfasis]. Cuando tengas el diseño listo, pásalo al Productor con el contexto completo.
```

#### **Template para Proyectos Complejos:**
```
🎯 CONTEXTO ESTRATÉGICO EXPANDIDO:
- Visión a largo plazo: [visión 2-5 años]
- Fase actual del proyecto: [dónde estamos en el roadmap]
- Dependencias externas: [qué necesitamos de otros equipos/sistemas]
- Riesgos estratégicos: [qué podría fallar y contingencias]

🧠 DECISIONES CLAVE TOMADAS:
- Alternativas evaluadas: [opciones consideradas y por qué se descartaron]
- Trade-offs aceptados: [qué sacrificamos por qué beneficios]
- Assumptions críticas: [qué asumimos que debe validarse]

🎨 BRIEF DE DISEÑO DETALLADO:
- Casos de uso principales: [user stories priorizadas]
- Flujos críticos: [journeys más importantes]
- Integraciones requeridas: [sistemas que deben conectarse]
- Escalabilidad esperada: [volumen y crecimiento proyectado]

🔄 PRÓXIMOS PASOS:
Actúa como Diseñador de Sistemas. Necesito que traduzcas esta estrategia en una arquitectura técnica robusta. Prioriza [aspecto específico] y asegúrate de que el diseño sea escalable para [contexto de crecimiento].
```

### **🎨 DISEÑADOR → PRODUCTOR**

#### **Template Estándar:**
```
📐 DISEÑO COMPLETADO:
- Arquitectura general: [overview de la solución]
- Componentes principales: [módulos y sus funciones]
- Flujos de trabajo: [cómo interactúan los componentes]
- Especificaciones técnicas: [detalles de implementación]

⚙️ PLAN DE IMPLEMENTACIÓN:
- Fases sugeridas: [secuencia lógica de desarrollo]
- Dependencias críticas: [qué debe hacerse antes de qué]
- Recursos estimados: [tiempo y skills necesarios]
- Riesgos técnicos: [qué podría complicarse]

🎯 CRITERIOS DE ACEPTACIÓN:
- Definición de "terminado": [qué constituye completitud]
- Tests requeridos: [cómo validar cada componente]
- Performance esperado: [métricas de rendimiento]
- Calidad mínima: [estándares no negociables]

📊 MÉTRICAS DE SEGUIMIENTO:
- Indicadores de progreso: [cómo medir avance]
- Señales de alerta: [qué indica problemas]
- Checkpoints de validación: [cuándo revisar y ajustar]

🔄 PRÓXIMOS PASOS:
Actúa como Productor. Toma este diseño y crea un plan de ejecución detallado. Coordina con el Ejecutor para asegurar que entienda cada componente. Prioriza [aspecto específico] y mantén comunicación constante sobre el progreso.
```

### **⚙️ PRODUCTOR → EJECUTOR**

#### **Template Estándar:**
```
📋 PLAN DE EJECUCIÓN:
- Tareas priorizadas: [backlog ordenado por importancia]
- Timeline detallado: [cronograma con hitos]
- Recursos asignados: [qué tienes disponible]
- Dependencias externas: [qué necesitas de otros]

🎯 ESPECIFICACIONES DE TRABAJO:
- Requerimientos funcionales: [qué debe hacer]
- Requerimientos no funcionales: [cómo debe comportarse]
- Estándares de calidad: [criterios de aceptación]
- Documentación requerida: [qué documentar y cómo]

🔧 HERRAMIENTAS Y RECURSOS:
- Stack tecnológico: [tecnologías a usar]
- Ambientes disponibles: [dev, test, prod]
- Accesos y permisos: [qué tienes autorizado]
- Contactos de apoyo: [a quién recurrir si hay problemas]

📊 REPORTING Y COMUNICACIÓN:
- Frecuencia de updates: [cada cuánto reportar]
- Formato de reporte: [cómo comunicar progreso]
- Escalación de problemas: [cuándo y cómo escalar]
- Canales de comunicación: [dónde y cómo comunicarse]

🔄 PRÓXIMOS PASOS:
Actúa como Ejecutor. Implementa este plan siguiendo las especificaciones exactas. Enfócate primero en [tarea prioritaria]. Reporta progreso [frecuencia] y escala inmediatamente si encuentras blockers. Cuando completes, pasa al Evaluador para validación.
```

### **🔨 EJECUTOR → EVALUADOR**

#### **Template Estándar:**
```
✅ ENTREGABLE COMPLETADO:
- Componentes implementados: [qué se construyó]
- Funcionalidades incluidas: [features implementadas]
- Documentación generada: [docs técnicas y de usuario]
- Tests realizados: [qué se probó y resultados]

🔧 DETALLES TÉCNICOS:
- Arquitectura implementada: [cómo se construyó]
- Decisiones de implementación: [choices técnicos tomados]
- Desviaciones del diseño: [qué se cambió y por qué]
- Issues conocidos: [problemas identificados pero no resueltos]

📊 MÉTRICAS DE PERFORMANCE:
- Benchmarks alcanzados: [performance medido]
- Cobertura de tests: [% de código probado]
- Métricas de calidad: [code quality, complexity, etc.]
- Uso de recursos: [memoria, CPU, storage]

🎯 CRITERIOS DE VALIDACIÓN:
- Checklist de aceptación: [criterios del Diseñador]
- Casos de prueba: [scenarios a validar]
- Edge cases: [situaciones límite a probar]
- Integración: [cómo se conecta con otros componentes]

🔄 PRÓXIMOS PASOS:
Actúa como Evaluador. Valida este entregable contra todos los criterios originales del Estratega y las especificaciones del Diseñador. Enfócate especialmente en [área crítica]. Si encuentras issues, documéntalos claramente y sugiere correcciones específicas.
```

### **🔍 EVALUADOR → ESTRATEGA**

#### **Template Estándar:**
```
📊 EVALUACIÓN COMPLETADA:
- Status general: [APROBADO/REQUIERE AJUSTES/RECHAZADO]
- Criterios cumplidos: [qué está bien]
- Gaps identificados: [qué falta o está mal]
- Calidad general: [assessment holístico]

🎯 ALINEACIÓN CON OBJETIVOS:
- Objetivos estratégicos cumplidos: [cuáles se lograron]
- Desviaciones de la visión: [qué no coincide]
- Impacto en stakeholders: [cómo afecta a usuarios finales]
- ROI proyectado: [valor esperado vs. inversión]

🔧 ISSUES Y RECOMENDACIONES:
- Problemas críticos: [qué debe arreglarse obligatoriamente]
- Mejoras sugeridas: [optimizaciones recomendadas]
- Riesgos identificados: [qué podría fallar en producción]
- Próximos pasos sugeridos: [qué hacer después]

📈 MÉTRICAS Y EVIDENCIA:
- KPIs alcanzados: [métricas vs. targets]
- Tests realizados: [evidencia de calidad]
- Feedback de usuarios: [si aplica]
- Benchmarks de performance: [datos objetivos]

🔄 DECISIÓN REQUERIDA:
Actúa como Estratega. Basado en esta evaluación, decide si: 1) Aprobar para entrega, 2) Solicitar ajustes específicos, o 3) Reconsiderar la estrategia. Si requiere ajustes, especifica exactamente qué cambiar y pasa de vuelta al [rol apropiado].
```

## 🎛️ Protocolos Adaptativos

### **Traspaso de Emergencia** (Fast-Track)
```
🚨 CONTEXTO DE URGENCIA:
- Situación crítica: [qué pasó]
- Timeline comprimido: [nuevo deadline]
- Recursos limitados: [qué tenemos disponible]
- Calidad mínima aceptable: [qué no podemos sacrificar]

⚡ ACCIÓN INMEDIATA REQUERIDA:
[Instrucciones específicas y directas]

🔄 PRÓXIMO PASO:
Actúa como [Rol]. PRIORIDAD MÁXIMA: [acción específica]. Reporta status en [timeframe corto].
```

### **Traspaso Iterativo** (Agile)
```
🔄 ITERACIÓN #[N]:
- Sprint goal: [objetivo específico de esta iteración]
- User stories: [funcionalidades a implementar]
- Definition of done: [criterios de completitud]
- Retrospective anterior: [learnings de la iteración pasada]

📊 MÉTRICAS DE SPRINT:
- Velocity target: [story points esperados]
- Capacity: [horas disponibles]
- Dependencies: [qué necesitamos de otros]

🔄 PRÓXIMOS PASOS:
Actúa como [Rol]. Enfócate en completar [prioridad #1] en esta iteración. Daily sync a las [hora] para ajustes rápidos.
```

### **Traspaso Paralelo** (Multi-Stream)
```
🔀 WORKSTREAM: [Nombre del stream]
- Objetivo específico: [qué debe lograr este stream]
- Dependencias con otros streams: [qué necesita/proporciona]
- Sync points: [cuándo sincronizar con otros streams]
- Integration plan: [cómo se juntará todo]

🎯 RESPONSABILIDAD ESPECÍFICA:
[Scope exacto de este workstream]

🔄 PRÓXIMOS PASOS:
Actúa como [Rol] para el workstream [nombre]. Coordina con [otros roles] en los sync points programados. Escala inmediatamente si hay conflictos de integración.
```

## 🔧 Automatización de Traspasos

### **Triggers Automáticos:**
```python
# Ejemplo de lógica de traspaso automático
if task.status == "COMPLETED" and task.quality_score >= threshold:
    next_role = workflow.get_next_role(current_role)
    handoff_context = generate_handoff_template(task, next_role)
    notify_next_role(next_role, handoff_context)
    log_handoff(current_role, next_role, task.id, timestamp)
```

### **Validación Automática:**
- **Completitud Check:** Verificar que todos los campos requeridos estén llenos
- **Quality Gate:** Validar que se cumplan métricas mínimas
- **Dependency Check:** Confirmar que dependencias estén resueltas
- **Resource Availability:** Verificar que el siguiente rol esté disponible

### **Escalación Automática:**
```
IF handoff_pending > 24_hours THEN
    escalate_to_producer()
IF quality_score < minimum_threshold THEN
    return_to_previous_role()
IF dependency_blocked > 48_hours THEN
    escalate_to_strategist()
```

## 📋 Checklist de Validación de Traspaso

### **Pre-Handoff Validation:**
- [ ] Contexto completo incluido
- [ ] Criterios de aceptación claros
- [ ] Dependencias identificadas
- [ ] Timeline realista
- [ ] Recursos confirmados disponibles
- [ ] Quality gates definidos

### **Post-Handoff Validation:**
- [ ] Recepción confirmada por siguiente rol
- [ ] Comprensión validada
- [ ] Dudas clarificadas
- [ ] Próximos pasos acordados
- [ ] Timeline confirmado
- [ ] Escalación paths establecidos

### **Continuous Monitoring:**
- [ ] Progress tracking activo
- [ ] Quality metrics monitoreadas
- [ ] Blockers identificados temprano
- [ ] Communication channels abiertos
- [ ] Feedback loops funcionando

## 🔗 ECO-HANDOFF BLOCK (Obligatorio en Cada Traspaso)

### **Estructura Estándar:**
```yaml
# === ECO-HANDOFF BLOCK ===
project_id: "PROJ-2024-001"
from_role: "Diseñador"
to_role: "Ejecutor"
timestamp: "2024-01-20T14:30:00Z"
phase: "Implementación"

# Qué se hizo y dónde
progress_summary:
  - item: "Arquitectura de base de datos completada"
    done_by: "Diseñador"
    artifact: "DB_Schema_v2.1"
    status: "done"
  - item: "API endpoints definidos"
    done_by: "Diseñador"
    artifact: "API_Spec_v1.3"
    status: "done"

# Decisiones y contexto
decisions:
  - id: "DEC-015"
    what: "PostgreSQL seleccionado sobre MongoDB"
    why: "Requerimientos ACID y queries complejas"
    impact: "Mejor consistencia, curva de aprendizaje para equipo"

# Estado y pendientes
current_state: "Diseño técnico completo, listo para implementación"
open_questions:
  - "¿Usar ORM o queries nativas para performance crítico?"
  - "¿Implementar caching desde v1 o en v2?"
next_actions:
  - "Configurar ambiente de desarrollo"
  - "Implementar modelos de datos"
  - "Crear endpoints básicos de CRUD"

# Métricas instantáneas
kpi_snapshot:
  quality: 8.5
  risk_level: "low"
  cycle_time_days: 3
  completion_percentage: 75

# Clasificación de seguridad
security:
  classification: "SENSITIVE"  # PUBLIC | SENSITIVE | CRITICAL
  sharing_rules: "Need-to-know basis; no literal citation of specs"
  retention: "Project lifecycle + 12 months"
  handling_notes: "Contains technical architecture details"

# Prompt sugerido para el rol receptor
handoff_prompt: |
  Actúa como Ejecutor Técnico. Tienes el diseño completo de la base de datos
  y especificaciones de API. Tu prioridad es implementar los modelos de datos
  primero, luego los endpoints CRUD básicos. Usa PostgreSQL como decidido.
  Consulta al Diseñador si encuentras inconsistencias en las specs.
# === FIN ECO-HANDOFF BLOCK ===
```

### **Variantes por Contexto:**

#### **ECO-HANDOFF Emergencia:**
```yaml
# === ECO-HANDOFF EMERGENCIA ===
project_id: "PROJ-2024-001"
from_role: "Productor"
to_role: "Ejecutor"
timestamp: "2024-01-20T16:45:00Z"
phase: "Crisis Management"
urgency: "CRITICAL"

# Situación crítica
crisis_summary: "Servidor de producción caído, usuarios sin acceso"
immediate_action: "Restaurar servicio usando backup de 2 horas atrás"
timeline: "Máximo 30 minutos para resolución"

# Contexto mínimo esencial
last_known_good_state: "2024-01-20T14:30:00Z - Deploy v2.1.3 exitoso"
suspected_cause: "Actualización de dependencias rompió compatibilidad"
rollback_plan: "Revertir a v2.1.2, investigar después"

# Próximos pasos inmediatos
next_actions:
  - "Ejecutar rollback script"
  - "Verificar funcionalidad crítica"
  - "Comunicar status a stakeholders"

# Clasificación de seguridad
security:
  classification: "CRITICAL"
  sharing_rules: "Emergency personnel only; immediate action required"
  retention: "Incident lifecycle + 24 months"
  handling_notes: "Contains production system details and access procedures"

handoff_prompt: |
  EMERGENCIA: Actúa como Ejecutor. Servidor caído. Ejecuta rollback inmediato
  a v2.1.2. Prioridad absoluta: restaurar servicio. Reporta status cada 10 min.
# === FIN ECO-HANDOFF EMERGENCIA ===
```

#### **ECO-HANDOFF Paralelo:**
```yaml
# === ECO-HANDOFF PARALELO ===
project_id: "PROJ-2024-001"
workstream: "Frontend_Development"
from_role: "Diseñador UX"
to_role: "Ejecutor Frontend"
timestamp: "2024-01-20T10:00:00Z"
phase: "Desarrollo Paralelo"

# Contexto del workstream
workstream_scope: "Interfaz de usuario para módulo de autenticación"
parallel_streams: ["Backend_API", "Database_Setup", "DevOps_Pipeline"]
sync_points: ["2024-01-22 - Integration", "2024-01-25 - Testing"]

# Dependencias con otros streams
dependencies:
  - stream: "Backend_API"
    needs: "Endpoints de login/logout/register"
    eta: "2024-01-21T17:00:00Z"
  - stream: "Database_Setup"
    needs: "User schema confirmado"
    eta: "2024-01-21T12:00:00Z"

# Coordinación
coordination_channel: "Slack #auth-module"
daily_sync: "09:00 UTC"
integration_lead: "Productor"

# Clasificación de seguridad
security:
  classification: "SENSITIVE"
  sharing_rules: "Workstream team members only; coordinate with other streams"
  retention: "Project lifecycle + 6 months"
  handling_notes: "Contains authentication system design and integration details"

handoff_prompt: |
  Actúa como Ejecutor Frontend para workstream de Autenticación.
  Desarrolla UI mockups primero, luego integra con API cuando esté listo.
  Sync diario 09:00 UTC. Escala blockers inmediatamente al Productor.
# === FIN ECO-HANDOFF PARALELO ===
```

## 📊 Memoria Compartida Ligera (Salida Obligatoria)

### **Logs Mínimos que Cada GPT Debe Producir:**
```markdown
### 🧠 MEMORIA DEL ECOSISTEMA (salida obligatoria)

**Decision Log (últimas 3 decisiones):**
- DEC-015: PostgreSQL vs MongoDB → PostgreSQL (ACID requirements)
- DEC-014: Monolito vs Microservicios → Monolito (team size)
- DEC-013: React vs Vue → React (team expertise)

**State Log (estado actual):**
- Fase: Implementación (75% completo)
- Progreso: Base de datos diseñada, API specs listas
- Bloqueos: Ninguno activo
- Risk Level: Bajo
- Next Milestone: MVP Demo (2024-01-30)

**Change Log (cambios recientes):**
- 2024-01-20: Scope +Social Login (stakeholder request, +2 días)
- 2024-01-18: Tech Stack PostgreSQL → mejor performance
- 2024-01-15: Timeline extendido 1 semana (resource constraint)
```

---

*Estos protocolos aseguran traspasos fluidos, contextualizados y trazables entre todos los roles del ecosistema, con memoria persistente y continuidad cognitiva garantizada.*
