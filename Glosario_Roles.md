Glosario_Roles.md

Este glosario define brevemente los roles comunes en un ecosistema colaborativo de GPTs:

Estratega: Establece la estrategia general del proyecto, definiendo objetivos, alcance y prioridades desde el inicio.

Diseñador: Crea la estructura o plan detallado para alcanzar los objetivos (por ejemplo, el esquema de un contenido o la arquitectura de una solución).

Productor: Gestiona los tiempos, recursos y la coordinación, asegurando que el plan se ejecute de forma ordenada y dentro de los plazos.

Ejecutor: Ejecuta las tareas concretas según el plan establecido, produciendo el entregable final (texto, có<PERSON>, an<PERSON><PERSON><PERSON>, etc.).

Evaluador: Verifica la calidad del resultado final y su cumplimiento con los criterios definidos, detectando errores o áreas de mejora y proponiendo correcciones.